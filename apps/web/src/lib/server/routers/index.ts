import {
  publicProcedure,
  protectedProcedure,
  adminProcedure,
  router,
} from "../trpc";
import { supabase, createServerSupabaseClient, type ContentPiece, type SearchResult, type Group, type GroupContent, type GroupWithContent } from "../supabase";
import { z } from "zod";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Get Twitter Spaces (sorted by tune-in numbers)
  getTwitterSpaces: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .eq('twitter_content_type', 'space')
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch Twitter Spaces: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get Marketing Case Studies (only content with marketing type)
  getMarketingCaseStudies: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .contains('content_types', ['marketing'])
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch marketing case studies: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get Testimonials (only content with testimonials type)
  getTestimonials: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .contains('content_types', ['testimonials'])
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch testimonials: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Search all content
  searchContent: publicProcedure
    .input(z.object({
      query: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .rpc('search_content_pieces', {
          search_query: input.query,
          limit_count: input.limit
        });

      if (error) {
        throw new Error(`Search failed: ${error.message}`);
      }

      return data as SearchResult[];
    }),

  // Get all content with pagination
  getAllContent: publicProcedure
    .input(z.object({
      page: z.number().default(0),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .order('content_created_date', { ascending: false })
        .range(input.page * input.limit, (input.page + 1) * input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Get Twitter Tweets (sorted by impressions)
  getTwitterTweets: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .eq('twitter_content_type', 'tweet')
      .order('twitter_impressions', { ascending: false })
      .limit(20);

    if (error) {
      throw new Error(`Failed to fetch Twitter tweets: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get all Twitter content (Spaces + Tweets)
  getAllTwitterContent: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .contains('content_types', ['twitter'])
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch Twitter content: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get content by category
  getContentByCategory: publicProcedure
    .input(z.object({
      category: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_categories', [input.category])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by category: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Get content by type (twitter, marketing, etc.)
  getContentByType: publicProcedure
    .input(z.object({
      type: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_types', [input.type])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by type: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Advanced filtered content with sorting
  getFilteredContent: publicProcedure
    .input(z.object({
      search: z.string().default(""),
      contentTypes: z.array(z.string()).default([]),
      categories: z.array(z.string()).default([]),
      sortBy: z.enum(["impressions", "date", "likes", "retweets"]).default("impressions"),
      sortOrder: z.enum(["desc", "asc"]).default("desc"),
      limit: z.number().default(30)
    }))
    .query(async ({ input }) => {
      let query = supabase.from('content_pieces').select('*');

      // Apply content type filters
      if (input.contentTypes.length > 0) {
        const contentTypeConditions = input.contentTypes.map(type => {
          switch (type) {
            case "spaces":
              return "twitter_content_type.eq.space";
            case "tweets":
              return "twitter_content_type.eq.tweet";
            case "marketing":
            case "twitter":
            case "presskit":
            case "incubation":
            case "testimonials":
              return `content_types.cs.{${type}}`;
            default:
              return null;
          }
        }).filter(Boolean);

        if (contentTypeConditions.length > 0) {
          query = query.or(contentTypeConditions.join(','));
        }
      }

      // Apply category filters - content must contain at least one of the selected categories
      if (input.categories.length > 0) {
        const categoryConditions = input.categories.map(category => 
          `content_categories.cs.{${category}}`
        );
        query = query.or(categoryConditions.join(','));
      }

      // Apply search filter
      if (input.search && input.search.length > 0) {
        query = query.or(`content_title.ilike.%${input.search}%,content_description.ilike.%${input.search}%,content_account.ilike.%${input.search}%`);
      }

      // Apply sorting
      const sortColumn = input.sortBy === "date" ? "content_created_date" : 
                        input.sortBy === "impressions" ? "twitter_impressions" :
                        input.sortBy === "likes" ? "twitter_likes" : "twitter_retweets";
      
      query = query.order(sortColumn, { ascending: input.sortOrder === "asc" });

      // Apply limit
      query = query.limit(input.limit);

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch filtered content: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Auth procedures
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    return ctx.user;
  }),

  signUp: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string().min(6),
    }))
    .mutation(async ({ input }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase.auth.signUp({
        email: input.email,
        password: input.password,
        options: {
          emailRedirectTo: `${process.env.CORS_ORIGIN || 'http://localhost:3001'}/auth/callback`,
        },
      });

      if (error) {
        throw new Error(`Sign up failed: ${error.message}`);
      }

      return { user: data.user, session: data.session };
    }),

  signIn: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string(),
    }))
    .mutation(async ({ input }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: input.email,
        password: input.password,
      });

      if (error) {
        throw new Error(`Sign in failed: ${error.message}`);
      }

      return { user: data.user, session: data.session };
    }),

  signOut: publicProcedure.mutation(async () => {
    const supabase = await createServerSupabaseClient();
    
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }

    return { success: true };
  }),

  signInWithGoogle: publicProcedure.mutation(async () => {
    const supabase = await createServerSupabaseClient();
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${process.env.CORS_ORIGIN || 'http://localhost:3001'}/auth/callback`,
      },
    });

    if (error) {
      throw new Error(`Google sign in failed: ${error.message}`);
    }

    return { url: data.url };
  }),

  // Admin content management procedures
  
  // Get user profile and role (safe for unauthenticated users)
  getUserProfile: publicProcedure.query(async ({ ctx }) => {
    console.log("🔍 [tRPC getUserProfile] Context state:", {
      hasUser: !!ctx.user,
      userEmail: ctx.user?.email,
      hasUserProfile: !!ctx.userProfile,
      userProfileRole: ctx.userProfile?.role
    });
    
    // If context doesn't have userProfile but has user, try to fetch it directly
    if (ctx.user && !ctx.userProfile) {
      console.log("🔍 [tRPC getUserProfile] Context missing profile, attempting direct fetch");
      
      try {
        const supabase = await createServerSupabaseClient();
        
        // Try direct query with service role to bypass RLS temporarily
        const { data: profile, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', ctx.user.id)
          .single();
          
        console.log("🔍 [tRPC getUserProfile] Direct query result:", {
          hasProfile: !!profile,
          profileData: profile,
          error: error?.message
        });
        
        if (profile) {
          return profile;
        }
      } catch (directError) {
        console.error("❌ [tRPC getUserProfile] Direct query failed:", directError);
      }
    }
    
    // Return null if not authenticated, otherwise return profile
    return ctx.userProfile || null;
  }),

  // Get user profile and role (requires authentication)
  getAuthenticatedUserProfile: protectedProcedure.query(async ({ ctx }) => {
    return ctx.userProfile;
  }),

  // Create new content entry (admin only)
  createContent: adminProcedure
    .input(z.object({
      content_uuid: z.string(),
      content_link: z.string().url(),
      content_tags: z.array(z.string()).default([]),
      content_account: z.string(),
      content_created_date: z.string(),
      content_types: z.array(z.string()).default([]),
      twitter_content_type: z.string().nullable().optional(),
      twitter_impressions: z.number().default(0),
      twitter_likes: z.number().default(0),
      twitter_retweets: z.number().default(0),
      content_title: z.string().nullable().optional(),
      content_description: z.string().nullable().optional(),
      content_categories: z.array(z.string()).default([]),
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .insert([input])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create content: ${error.message}`);
      }

      return data as ContentPiece;
    }),

  // Update existing content entry (admin only)
  updateContent: adminProcedure
    .input(z.object({
      id: z.number(),
      content_uuid: z.string().optional(),
      content_link: z.string().url().optional(),
      content_tags: z.array(z.string()).optional(),
      content_account: z.string().optional(),
      content_created_date: z.string().optional(),
      content_types: z.array(z.string()).optional(),
      twitter_content_type: z.string().nullable().optional(),
      twitter_impressions: z.number().optional(),
      twitter_likes: z.number().optional(),
      twitter_retweets: z.number().optional(),
      content_title: z.string().nullable().optional(),
      content_description: z.string().nullable().optional(),
      content_categories: z.array(z.string()).optional(),
    }))
    .mutation(async ({ input }) => {
      const { id, ...updates } = input;
      
      const { data, error } = await supabase
        .from('content_pieces')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update content: ${error.message}`);
      }

      return data as ContentPiece;
    }),

  // Delete content entry (admin only)
  deleteContent: adminProcedure
    .input(z.object({
      id: z.number(),
    }))
    .mutation(async ({ input }) => {
      const { error } = await supabase
        .from('content_pieces')
        .delete()
        .eq('id', input.id);

      if (error) {
        throw new Error(`Failed to delete content: ${error.message}`);
      }

      return { success: true };
    }),

  // Get single content entry by ID (admin only)
  getContentById: adminProcedure
    .input(z.object({
      id: z.number(),
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .eq('id', input.id)
        .single();

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return data as ContentPiece;
    }),

  // Bulk create content entries (admin only)
  bulkCreateContent: adminProcedure
    .input(z.object({
      entries: z.array(z.object({
        content_uuid: z.string(),
        content_link: z.string().url(),
        content_tags: z.array(z.string()).default([]),
        content_account: z.string(),
        content_created_date: z.string(),
        content_types: z.array(z.string()).default([]),
        twitter_content_type: z.string().nullable().optional(),
        twitter_impressions: z.number().default(0),
        twitter_likes: z.number().default(0),
        twitter_retweets: z.number().default(0),
        content_title: z.string().nullable().optional(),
        content_description: z.string().nullable().optional(),
        content_categories: z.array(z.string()).default([]),
      }))
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .insert(input.entries)
        .select();

      if (error) {
        throw new Error(`Bulk create failed: ${error.message}`);
      }

      return {
        success: true,
        created: data?.length || 0,
        entries: data as ContentPiece[]
      };
    }),

  // Get impact metrics for landing page
  getImpactMetrics: publicProcedure.query(async () => {
    // Get total impressions
    const { data: impressionsData, error: impressionsError } = await supabase
      .from('content_pieces')
      .select('twitter_impressions');

    if (impressionsError) {
      throw new Error(`Failed to fetch impressions: ${impressionsError.message}`);
    }

    const totalImpressions = impressionsData?.reduce((sum, item) => sum + (item.twitter_impressions || 0), 0) || 0;

    // Get total content count
    const { count: totalContent, error: countError } = await supabase
      .from('content_pieces')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(`Failed to fetch content count: ${countError.message}`);
    }

    // Calculate average engagement rate (likes + retweets / impressions)
    const { data: engagementData, error: engagementError } = await supabase
      .from('content_pieces')
      .select('twitter_impressions, twitter_likes, twitter_retweets')
      .gt('twitter_impressions', 0);

    if (engagementError) {
      throw new Error(`Failed to fetch engagement data: ${engagementError.message}`);
    }

    let totalEngagementRate = 0;
    let validEntries = 0;

    engagementData?.forEach(item => {
      if (item.twitter_impressions && item.twitter_impressions > 0) {
        const engagementRate = ((item.twitter_likes || 0) + (item.twitter_retweets || 0)) / item.twitter_impressions;
        totalEngagementRate += engagementRate;
        validEntries++;
      }
    });

    const avgEngagementRate = validEntries > 0 ? (totalEngagementRate / validEntries) * 100 : null;

    return {
      totalImpressions,
      totalContent: totalContent || 0,
      avgEngagementRate: avgEngagementRate !== null ? Math.round(avgEngagementRate * 10) / 10 : null // Round to 1 decimal place
    };
  }),

  // Get featured content for landing page
  getFeaturedContent: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .order('twitter_impressions', { ascending: false })
      .limit(6);

    if (error) {
      throw new Error(`Failed to fetch featured content: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Update stats table with current calculations (admin only)
  updateStats: adminProcedure.mutation(async () => {
    try {
      // Calculate current stats using the same logic as getImpactMetrics
      const { data: impressionsData } = await supabase
        .from('content_pieces')
        .select('twitter_impressions');

      const totalImpressions = impressionsData?.reduce((sum, item) => sum + (item.twitter_impressions || 0), 0) || 0;

      const { count: totalContent } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact', head: true });

      const { data: engagementData } = await supabase
        .from('content_pieces')
        .select('twitter_impressions, twitter_likes, twitter_retweets')
        .gt('twitter_impressions', 0);

      let totalEngagementRate = 0;
      let validEntries = 0;

      engagementData?.forEach(item => {
        if (item.twitter_impressions && item.twitter_impressions > 0) {
          const engagementRate = ((item.twitter_likes || 0) + (item.twitter_retweets || 0)) / item.twitter_impressions;
          totalEngagementRate += engagementRate;
          validEntries++;
        }
      });

      const avgEngagementRate = validEntries > 0 ? (totalEngagementRate / validEntries) * 100 : null;

      // Insert or update stats in the stats table
      const today = new Date().toISOString().split('T')[0];
      
      const statsToUpdate = [
        { stat_type: 'total_impressions', stat_value: totalImpressions },
        { stat_type: 'total_content', stat_value: totalContent || 0 },
        { stat_type: 'avg_engagement_rate', stat_value: avgEngagementRate }
      ];

      for (const stat of statsToUpdate) {
        if (stat.stat_value !== null) {
          // Use upsert to update existing or insert new
          const { error } = await supabase
            .from('stats')
            .upsert({
              stat_type: stat.stat_type,
              stat_value: stat.stat_value,
              period_start: today,
              period_end: today,
              calculated_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'stat_type,period_start,period_end'
            });

          if (error) {
            console.error(`Error updating ${stat.stat_type}:`, error);
          }
        }
      }

      return { 
        success: true, 
        message: 'Stats updated successfully',
        stats: {
          totalImpressions,
          totalContent: totalContent || 0,
          avgEngagementRate: avgEngagementRate !== null ? Math.round(avgEngagementRate * 10) / 10 : null
        }
      };
    } catch (error) {
      throw new Error(`Failed to update stats: ${error}`);
    }
  }),

  // Get stats from stats table with fallback to calculated values
  getImpactMetricsFromStats: publicProcedure.query(async () => {
    try {
      // Try to get stats from the stats table first
      const { data: statsData, error: statsError } = await supabase
        .from('stats')
        .select('stat_type, stat_value, calculated_at')
        .order('calculated_at', { ascending: false });

      if (statsError) {
        console.warn('Error fetching from stats table, falling back to calculated values:', statsError);
      }

      // If we have recent stats (within last 24 hours), use them
      const recentStats = statsData?.filter(stat => {
        const statDate = new Date(stat.calculated_at);
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return statDate > oneDayAgo;
      });

      if (recentStats && recentStats.length >= 2) {
        const statsMap = recentStats.reduce((acc, stat) => {
          acc[stat.stat_type] = stat.stat_value;
          return acc;
        }, {} as Record<string, number>);

        return {
          totalImpressions: statsMap.total_impressions || 0,
          totalContent: statsMap.total_content || 0,
          avgEngagementRate: statsMap.avg_engagement_rate || null
        };
      }

      // Fallback to calculated values if no recent stats
      console.log('No recent stats found, calculating on the fly...');
      
      const { data: impressionsData } = await supabase
        .from('content_pieces')
        .select('twitter_impressions');
      const totalImpressions = impressionsData?.reduce((sum, item) => sum + (item.twitter_impressions || 0), 0) || 0;

      const { count: totalContent } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact', head: true });

      const { data: engagementData } = await supabase
        .from('content_pieces')
        .select('twitter_impressions, twitter_likes, twitter_retweets')
        .gt('twitter_impressions', 0);

      let totalEngagementRate = 0;
      let validEntries = 0;
      engagementData?.forEach(item => {
        if (item.twitter_impressions && item.twitter_impressions > 0) {
          const engagementRate = ((item.twitter_likes || 0) + (item.twitter_retweets || 0)) / item.twitter_impressions;
          totalEngagementRate += engagementRate;
          validEntries++;
        }
      });
      const avgEngagementRate = validEntries > 0 ? (totalEngagementRate / validEntries) * 100 : null;

      return {
        totalImpressions,
        totalContent: totalContent || 0,
        avgEngagementRate: avgEngagementRate !== null ? Math.round(avgEngagementRate * 10) / 10 : null
      };
    } catch (error) {
      console.error('Error in getImpactMetricsFromStats:', error);
      throw error;
    }
  }),

  // Groups feature procedures

  // Create a new group
  createGroup: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(100),
      description: z.string().max(500).optional(),
      is_public: z.boolean().default(true)
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('groups')
        .insert([{
          name: input.name,
          description: input.description || null,
          is_public: input.is_public,
          user_id: ctx.user.id
        }])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create group: ${error.message}`);
      }

      return data as Group;
    }),

  // Get all user's groups (temporarily public for debugging)
  getGroups: publicProcedure
    .input(z.object({
      limit: z.number().default(50)
    }))
    .query(async ({ input, ctx }) => {
      console.log("🔍 [TRPC getGroups] Context:", { hasUser: !!ctx.user, hasProfile: !!ctx.userProfile });

      // Temporary bypass - if no user in context, return empty array
      if (!ctx.user) {
        console.log("⚠️ [TRPC getGroups] No user in context, returning empty array");
        return [];
      }
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('groups')
        .select(`
          *,
          group_content(count)
        `)
        .eq('user_id', ctx.user.id)
        .order('created_at', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch groups: ${error.message}`);
      }

      return data?.map(group => ({
        ...group,
        content_count: group.group_content?.[0]?.count || 0
      })) as (Group & { content_count: number })[];
    }),

  // Get single group with content
  getGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      limit: z.number().default(50)
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // First get the group - allow access if user owns it OR if it's public
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('*')
        .eq('id', input.id)
        .or(`user_id.eq.${ctx.user.id},is_public.eq.true`)
        .single();

      if (groupError) {
        throw new Error(`Failed to fetch group: ${groupError.message}`);
      }

      // Then get the content with join
      const { data: content, error: contentError } = await supabase
        .from('group_content')
        .select(`
          position,
          added_at,
          content_pieces (*)
        `)
        .eq('group_id', input.id)
        .order('position', { ascending: true })
        .limit(input.limit);

      if (contentError) {
        throw new Error(`Failed to fetch group content: ${contentError.message}`);
      }

      const groupWithContent: GroupWithContent = {
        ...group,
        content: content?.map((item: any) => ({
          ...item.content_pieces,
          position: item.position,
          added_at: item.added_at
        })) || [],
        content_count: content?.length || 0
      };

      return groupWithContent;
    }),

  // Get public group with content (unauthenticated access)
  getPublicGroup: publicProcedure
    .input(z.object({
      id: z.string().uuid(),
      limit: z.number().default(50)
    }))
    .query(async ({ input }) => {
      // Use basic supabase client for public access without authentication
      const { supabase } = await import("../supabase");
      
      console.log(`🔍 [getPublicGroup] Searching for public group with ID: ${input.id}`);
      
      // Get public group only - use .maybeSingle() to handle potential no-results case
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('*')
        .eq('id', input.id)
        .eq('is_public', true)
        .maybeSingle();

      console.log(`🔍 [getPublicGroup] Query result:`, { group, groupError });

      if (groupError) {
        console.error(`❌ [getPublicGroup] Database error:`, groupError);
        throw new Error(`Failed to fetch public group: ${groupError.message}`);
      }

      if (!group) {
        console.log(`❌ [getPublicGroup] Group not found or not public: ${input.id}`);
        throw new Error(`Public group not found`);
      }

      // Get the content with join
      console.log(`🔍 [getPublicGroup] Fetching content for group: ${input.id}`);
      const { data: content, error: contentError } = await supabase
        .from('group_content')
        .select(`
          position,
          added_at,
          content_pieces (*)
        `)
        .eq('group_id', input.id)
        .order('position', { ascending: true })
        .limit(input.limit);

      console.log(`🔍 [getPublicGroup] Content query result:`, { content, contentError });

      if (contentError) {
        console.error(`❌ [getPublicGroup] Content fetch error:`, contentError);
        throw new Error(`Failed to fetch group content: ${contentError.message}`);
      }

      const groupWithContent: GroupWithContent = {
        ...group,
        content: content?.map((item: any) => ({
          ...item.content_pieces,
          position: item.position,
          added_at: item.added_at
        })) || [],
        content_count: content?.length || 0
      };

      console.log(`✅ [getPublicGroup] Successfully retrieved public group:`, {
        groupId: group.id,
        groupName: group.name,
        contentCount: groupWithContent.content_count
      });

      return groupWithContent;
    }),

  // Update group
  updateGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      name: z.string().min(1).max(100).optional(),
      description: z.string().max(500).optional(),
      is_public: z.boolean().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      const { id, ...updates } = input;
      
      const { data, error } = await supabase
        .from('groups')
        .update(updates)
        .eq('id', id)
        .eq('user_id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update group: ${error.message}`);
      }

      return data as Group;
    }),

  // Get shareable link for a group
  getGroupShareLink: protectedProcedure
    .input(z.object({
      id: z.string().uuid()
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group
      const { data: group, error } = await supabase
        .from('groups')
        .select('id, name, is_public')
        .eq('id', input.id)
        .eq('user_id', ctx.user.id)
        .single();

      if (error || !group) {
        throw new Error('Group not found or access denied');
      }

      if (!group.is_public) {
        throw new Error('Only public groups can be shared');
      }

      const baseUrl = process.env.CORS_ORIGIN || 'http://localhost:3001';
      const shareUrl = `${baseUrl}/groups/share/${group.id}`;

      return {
        shareUrl,
        groupName: group.name,
        isPublic: group.is_public
      };
    }),

  // Get public groups for discovery
  getPublicGroups: publicProcedure
    .input(z.object({
      limit: z.number().default(20),
      search: z.string().optional()
    }))
    .query(async ({ input }) => {
      // Use basic supabase client for public access without authentication
      const { supabase } = await import("../supabase");
      
      let query = supabase
        .from('groups')
        .select(`
          *,
          group_content(count)
        `)
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(input.limit);

      // Add search filter if provided
      if (input.search && input.search.length > 0) {
        query = query.or(`name.ilike.%${input.search}%,description.ilike.%${input.search}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch public groups: ${error.message}`);
      }

      return data?.map(group => ({
        ...group,
        content_count: group.group_content?.[0]?.count || 0
      })) as (Group & { content_count: number })[];
    }),

  // Delete group
  deleteGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { error } = await supabase
        .from('groups')
        .delete()
        .eq('id', input.id)
        .eq('user_id', ctx.user.id);

      if (error) {
        throw new Error(`Failed to delete group: ${error.message}`);
      }

      return { success: true };
    }),

  // Add content to group
  addContentToGroup: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_id: z.number(),
      position: z.number().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('id')
        .eq('id', input.group_id)
        .eq('user_id', ctx.user.id)
        .single();

      if (groupError || !group) {
        throw new Error('Group not found or access denied');
      }

      // Get the next position if not provided
      let position = input.position;
      if (position === undefined) {
        const { data: maxPos } = await supabase
          .from('group_content')
          .select('position')
          .eq('group_id', input.group_id)
          .order('position', { ascending: false })
          .limit(1);
        
        position = (maxPos?.[0]?.position ?? -1) + 1;
      }

      const { data, error } = await supabase
        .from('group_content')
        .insert([{
          group_id: input.group_id,
          content_id: input.content_id,
          position
        }])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to add content to group: ${error.message}`);
      }

      return data as GroupContent;
    }),

  // Remove content from group
  removeContentFromGroup: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_id: z.number()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group through the RLS policy
      const { error } = await supabase
        .from('group_content')
        .delete()
        .eq('group_id', input.group_id)
        .eq('content_id', input.content_id);

      if (error) {
        throw new Error(`Failed to remove content from group: ${error.message}`);
      }

      return { success: true };
    }),

  // Reorder content in group
  reorderGroupContent: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_positions: z.array(z.object({
        content_id: z.number(),
        position: z.number()
      }))
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Update positions for each content item
      const updates = input.content_positions.map(async ({ content_id, position }) => {
        return supabase
          .from('group_content')
          .update({ position })
          .eq('group_id', input.group_id)
          .eq('content_id', content_id);
      });

      const results = await Promise.all(updates);
      
      // Check for any errors
      const hasError = results.some(result => result.error);
      if (hasError) {
        throw new Error('Failed to reorder content');
      }

      return { success: true };
    }),

  // Check if content is in any user groups (for UI state) - temporarily public for debugging
  getContentGroupStatus: publicProcedure
    .input(z.object({
      content_id: z.number()
    }))
    .query(async ({ input, ctx }) => {
      console.log("🔍 [TRPC getContentGroupStatus] Context:", { hasUser: !!ctx.user, contentId: input.content_id });

      // Temporary bypass - if no user in context, return default state
      if (!ctx.user) {
        console.log("⚠️ [TRPC getContentGroupStatus] No user in context, returning default state");
        return { inGroup: false, groupName: null };
      }
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('group_content')
        .select(`
          group_id,
          groups!inner (
            id,
            name,
            user_id
          )
        `)
        .eq('content_id', input.content_id)
        .eq('groups.user_id', ctx.user.id);

      if (error) {
        throw new Error(`Failed to check content group status: ${error.message}`);
      }

      return data?.map((item: any) => ({
        group_id: item.group_id,
        group_name: item.groups.name
      })) || [];
    }),

  // Content Metadata Management Procedures (Admin Only)

  // Get all filter metadata with usage counts
  getFilterMetadata: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']).optional()
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase.rpc('get_filter_metadata', {
        filter_type: input.type || null
      });

      if (error) {
        throw new Error(`Failed to fetch filter metadata: ${error.message}`);
      }

      // Group the results by type
      const grouped = (data as Array<{type: string, value: string, usage_count: number}>).reduce((acc, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push({
          value: item.value,
          usageCount: item.usage_count
        });
        return acc;
      }, {} as Record<string, Array<{value: string, usageCount: number}>>);

      return grouped;
    }),

  // Rename a filter value across all content
  renameFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      oldValue: z.string(),
      newValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, oldValue, newValue } = input;
      
      let updateQuery;
      switch (type) {
        case 'content_account':
          updateQuery = supabase
            .from('content_pieces')
            .update({ content_account: newValue })
            .eq('content_account', oldValue);
          break;
        case 'twitter_content_type':
          updateQuery = supabase
            .from('content_pieces')
            .update({ twitter_content_type: newValue as any })
            .eq('twitter_content_type', oldValue);
          break;
        case 'content_tags':
          // For array fields, we need to use PostgreSQL array functions
          const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_tags',
            old_value: oldValue,
            new_value: newValue
          });
          if (tagError) throw new Error(`Failed to rename content tag: ${tagError.message}`);
          return { success: true, updatedCount: tagData };
        case 'content_types':
          const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_types',
            old_value: oldValue,
            new_value: newValue
          });
          if (typeError) throw new Error(`Failed to rename content type: ${typeError.message}`);
          return { success: true, updatedCount: typeData };
        case 'content_categories':
          const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
            table_name: 'content_pieces',
            column_name: 'content_categories',
            old_value: oldValue,
            new_value: newValue
          });
          if (catError) throw new Error(`Failed to rename content category: ${catError.message}`);
          return { success: true, updatedCount: catData };
      }

      const { data, error } = await updateQuery.select('id');
      if (error) {
        throw new Error(`Failed to rename ${type}: ${error.message}`);
      }

      return { success: true, updatedCount: data?.length || 0 };
    }),

  // Merge multiple filter values into one
  mergeFilterValues: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      sourceValues: z.array(z.string()),
      targetValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, sourceValues, targetValue } = input;
      let totalUpdated = 0;

      for (const sourceValue of sourceValues) {
        if (sourceValue === targetValue) continue; // Skip if already the target value

        let updateQuery;
        switch (type) {
          case 'content_account':
            updateQuery = supabase
              .from('content_pieces')
              .update({ content_account: targetValue })
              .eq('content_account', sourceValue);
            break;
          case 'twitter_content_type':
            updateQuery = supabase
              .from('content_pieces')
              .update({ twitter_content_type: targetValue as any })
              .eq('twitter_content_type', sourceValue);
            break;
          case 'content_tags':
            const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_tags',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (tagError) throw new Error(`Failed to merge content tag: ${tagError.message}`);
            totalUpdated += tagData || 0;
            continue;
          case 'content_types':
            const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_types',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (typeError) throw new Error(`Failed to merge content type: ${typeError.message}`);
            totalUpdated += typeData || 0;
            continue;
          case 'content_categories':
            const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
              table_name: 'content_pieces',
              column_name: 'content_categories',
              old_value: sourceValue,
              new_value: targetValue
            });
            if (catError) throw new Error(`Failed to merge content category: ${catError.message}`);
            totalUpdated += catData || 0;
            continue;
        }

        const { data, error } = await updateQuery.select('id');
        if (error) {
          throw new Error(`Failed to merge ${type}: ${error.message}`);
        }
        totalUpdated += data?.length || 0;
      }

      return { success: true, updatedCount: totalUpdated };
    }),

  // Delete unused filter values (only if usage count is 0)
  deleteFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { type, value, force } = input;

      // First check usage count
      const { data: usage, error: usageError } = await supabase.rpc('get_value_usage_count', {
        filter_type: type,
        filter_value: value
      });

      if (usageError) {
        throw new Error(`Failed to check usage count: ${usageError.message}`);
      }

      if (usage > 0 && !force) {
        throw new Error(`Cannot delete ${type} "${value}" as it is used in ${usage} content pieces. Use force=true to delete anyway.`);
      }

      // For array types, we can't really "delete" the value from unused records as arrays don't have unused values
      // For scalar types like content_account, we would need to set them to null or another value
      if (type === 'content_account') {
        // We can't really delete account names that aren't used since they're required fields
        // This operation would typically be a "cleanup" rather than delete
        return { success: true, message: `Value "${value}" marked for cleanup (${usage} usages would be affected)` };
      }

      return { success: true, message: `Value "${value}" processed (${usage} usages)` };
    }),

  // Add new filter value to predefined lists (mainly for enum management)
  addFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_types', 'twitter_content_type', 'content_categories']),
      value: z.string(),
      description: z.string().optional()
    }))
    .mutation(async ({ input }) => {
      const { type, value } = input;

      // Note: Adding new enum values requires database schema changes
      // This procedure documents the intended new values and could trigger migrations
      
      // For now, we'll store these as suggestions in a potential enum_suggestions table
      // or just return success indicating the value can be used in content creation
      
      return { 
        success: true, 
        message: `New ${type} value "${value}" can now be used in content creation. Database schema may need updating for enum enforcement.` 
      };
    }),
});

export type AppRouter = typeof appRouter;