"use client";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { AuthDebugPanel } from "@/components/debug/auth-debug";
import { SearchBar } from "@/components/search-bar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Shield, BarChart3, ChevronDown, X, ExternalLink, Settings, Database, Tag, User, Type, Hash } from "lucide-react";
import { z } from "zod";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

// Database enum constants
const CONTENT_TYPES = ["twitter", "presskit", "marketing", "incubation", "testimonials"] as const;
const TWITTER_CONTENT_TYPES = ["space", "interview", "tweet", "thread", "retweet"] as const;
const CONTENT_CATEGORIES = [
  "ai", "defi", "gaming", "memecoin", "web2", "crypto", "politics", 
  "news", "markets", "nft", "metaverse", "blockchain", "bitcoin", 
  "ethereum", "solana", "regulation", "technology", "finance"
] as const;

// Form validation schema matching tRPC backend
const contentFormSchema = z.object({
  content_uuid: z.string().min(1, "Content UUID is required"),
  content_link: z.string().url("Please enter a valid URL"),
  content_tags: z.array(z.string()).default([]),
  content_account: z.string().min(1, "Content account is required"),
  content_created_date: z.string().min(1, "Created date is required"),
  content_types: z.array(z.enum(CONTENT_TYPES)).default([]),
  twitter_content_type: z.enum(TWITTER_CONTENT_TYPES).nullable().optional(),
  twitter_impressions: z.number().min(0, "Impressions must be 0 or greater").default(0),
  twitter_likes: z.number().min(0, "Likes must be 0 or greater").default(0),
  twitter_retweets: z.number().min(0, "Retweets must be 0 or greater").default(0),
  content_title: z.string().nullable().optional(),
  content_description: z.string().nullable().optional(),
  content_categories: z.array(z.enum(CONTENT_CATEGORIES)).default([]),
});

type FormErrors = Partial<Record<keyof ContentFormData, string>>;

// Error display component
interface FieldErrorProps {
  error?: string;
}

const FieldError = ({ error }: FieldErrorProps) => {
  if (!error) return null;
  return <p className="text-sm text-destructive mt-1">{error}</p>;
};

// Multi-select dropdown component
interface MultiSelectDropdownProps {
  label: string;
  value: string[];
  options: readonly string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
}

const MultiSelectDropdown = ({ label, value, options, onChange, placeholder }: MultiSelectDropdownProps) => {
  const toggleOption = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter(item => item !== option));
    } else {
      onChange([...value, option]);
    }
  };

  const removeOption = (option: string) => {
    onChange(value.filter(item => item !== option));
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-auto min-h-10 px-3 py-2"
          >
            <div className="flex flex-wrap gap-1">
              {value.length === 0 ? (
                <span className="text-muted-foreground">{placeholder || "Select options..."}</span>
              ) : (
                value.map(item => (
                  <Badge 
                    key={item} 
                    variant="secondary" 
                    className="text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeOption(item);
                    }}
                  >
                    {item}
                    <X className="ml-1 h-3 w-3" />
                  </Badge>
                ))
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => toggleOption(option)}
              className="cursor-pointer"
            >
              <div className="flex items-center space-x-2">
                <div className={`w-4 h-4 border rounded ${value.includes(option) ? 'bg-primary border-primary' : 'border-input'}`}>
                  {value.includes(option) && <div className="w-full h-full bg-primary-foreground rounded-sm m-0.5" />}
                </div>
                <span className="capitalize">{option}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Single select dropdown component
interface SingleSelectDropdownProps {
  label: string;
  value: string | null;
  options: readonly string[];
  onChange: (value: string | null) => void;
  placeholder?: string;
  allowNull?: boolean;
}

const SingleSelectDropdown = ({ label, value, options, onChange, placeholder, allowNull = true }: SingleSelectDropdownProps) => {
  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <span className={value ? "" : "text-muted-foreground"}>
              {value ? value.charAt(0).toUpperCase() + value.slice(1) : (placeholder || "Select option...")}
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          {allowNull && (
            <DropdownMenuItem onClick={() => onChange(null)}>
              <span className="text-muted-foreground">None selected</span>
            </DropdownMenuItem>
          )}
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => onChange(option)}
              className="cursor-pointer"
            >
              <span className="capitalize">{option}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

interface ContentFormData {
  content_uuid: string;
  content_link: string;
  content_tags: string[];
  content_account: string;
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
}

const initialFormData: ContentFormData = {
  content_uuid: "",
  content_link: "",
  content_tags: [],
  content_account: "",
  content_created_date: new Date().toISOString().split('T')[0],
  content_types: [],
  twitter_content_type: null,
  twitter_impressions: 0,
  twitter_likes: 0,
  twitter_retweets: 0,
  content_title: null,
  content_description: null,
  content_categories: [],
};

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

// Helper function to format dates
const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return dateString;
  }
};

function AdminPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<ContentFormData>(initialFormData);
  const [showForm, setShowForm] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [activeTab, setActiveTab] = useState<'content' | 'metadata'>('content');

  console.log('Admin page loaded');

  // Initialize search state from URL parameters
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc"
  }));

  // Track if search is active
  const isSearchActive = searchQuery.length > 0 || filters.contentTypes.length > 0 || filters.categories.length > 0;

  // Update URL when search/filters change
  const updateURL = useCallback(() => {
    const params = new URLSearchParams();
    if (searchQuery) params.set("q", searchQuery);
    if (filters.contentTypes.length > 0) params.set("types", filters.contentTypes.join(","));
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== "impressions") params.set("sort", filters.sortBy);
    if (filters.sortOrder !== "desc") params.set("order", filters.sortOrder);

    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname;
    window.history.replaceState({}, "", newURL);
  }, [searchQuery, filters]);

  useEffect(() => {
    updateURL();
  }, [updateURL]);

  // Use the same authentication method as the header for consistency
  const { data: user, isLoading: userLoading } = trpc.getCurrentUser.useQuery(undefined, {
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Check if user is admin - now safe for unauthenticated users
  const userProfileQuery = trpc.getUserProfile.useQuery(undefined, {
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Comprehensive debugging
  console.log("🔍 [ADMIN PAGE] ==================== ADMIN PAGE STATE ====================");
  console.log("🔍 [ADMIN PAGE] getCurrentUser query:", {
    hasUser: !!user,
    userEmail: user?.email,
    userId: user?.id,
    userLoading,
    userError: userLoading ? 'Loading...' : (!user ? 'No user' : 'User found')
  });

  console.log("🔍 [ADMIN PAGE] getUserProfile query:", {
    profileLoading: userProfileQuery.isLoading,
    hasProfileData: !!userProfileQuery.data,
    profileData: userProfileQuery.data,
    profileError: userProfileQuery.error?.message,
    profileErrorCode: userProfileQuery.error?.data?.code,
    userRole: userProfileQuery.data?.role,
    queryStatus: userProfileQuery.status,
    queryFetchStatus: userProfileQuery.fetchStatus
  });

  // Additional debugging for profile data
  if (userProfileQuery.data) {
    console.log("✅ [ADMIN PAGE] Profile data found:", userProfileQuery.data);
  } else if (userProfileQuery.error) {
    console.log("❌ [ADMIN PAGE] Profile query error details:", {
      message: userProfileQuery.error.message,
      data: userProfileQuery.error.data,
      shape: userProfileQuery.error.shape
    });
  } else if (!userProfileQuery.isLoading) {
    console.log("⚠️ [ADMIN PAGE] Profile query returned null (no error, no data)");
  }

  // Debug localStorage/sessionStorage
  if (typeof window !== 'undefined') {
    console.log("🔍 [ADMIN PAGE] Browser storage:", {
      localStorage: Object.keys(localStorage).filter(k => k.includes('supabase')),
      sessionStorage: Object.keys(sessionStorage).filter(k => k.includes('supabase')),
      cookies: document.cookie.split(';').filter(c => c.includes('sb-')).map(c => c.trim().split('=')[0])
    });
  }

  // Get content for admin management
  const contentQuery = trpc.getAllContent.useQuery({
    page: 0,
    limit: 50,
  });

  // Get filtered content for search functionality
  const filteredContentQuery = trpc.getFilteredContent.useQuery({
    search: searchQuery,
    contentTypes: filters.contentTypes,
    categories: filters.categories,
    sortBy: filters.sortBy as "impressions" | "date" | "likes" | "retweets",
    sortOrder: filters.sortOrder as "desc" | "asc",
    limit: 50
  });

  // Get metadata for management (only fetch when metadata tab is active)
  const metadataQuery = trpc.getFilterMetadata.useQuery(undefined, {
    enabled: activeTab === 'metadata'
  });

  console.log('🔍 [ADMIN] Search state:', {
    searchQuery,
    filters,
    isSearchActive,
    filteredResults: filteredContentQuery.data?.length,
    allContent: contentQuery.data?.length
  });

  // Search handlers
  const handleSearch = (query: string) => {
    console.log('🔍 [ADMIN] Search query:', query);
    setSearchQuery(query);
  };

  const handleFilter = (filterType: string, values: string[]) => {
    console.log('🔍 [ADMIN] Filter change:', filterType, values);
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    console.log('🔍 [ADMIN] Sort change:', sortBy, sortOrder);
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder
    }));
  };

  // Mutations
  const createContentMutation = trpc.createContent.useMutation({
    onSuccess: () => {
      toast.success("Content created successfully!");
      setFormData(initialFormData);
      setShowForm(false);
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to create content: ${error.message}`);
    },
  });

  const deleteContentMutation = trpc.deleteContent.useMutation({
    onSuccess: () => {
      toast.success("Content deleted successfully!");
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content: ${error.message}`);
    },
  });

  const updateStatsMutation = trpc.updateStats.useMutation({
    onSuccess: (data) => {
      toast.success(`Stats updated successfully! ${data.stats.totalImpressions} impressions, ${data.stats.totalContent} content pieces`);
    },
    onError: (error) => {
      toast.error(`Failed to update stats: ${error.message}`);
    },
  });

  // Metadata management mutations
  const renameFilterMutation = trpc.renameFilterValue.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully renamed value. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to rename value: ${error.message}`);
    },
  });

  const mergeFilterMutation = trpc.mergeFilterValues.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully merged values. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to merge values: ${error.message}`);
    },
  });

  const deleteFilterMutation = trpc.deleteFilterValue.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
      contentQuery.refetch();
      filteredContentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete value: ${error.message}`);
    },
  });

  // Loading states
  if (userLoading || userProfileQuery.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Not authenticated - show login prompt
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-blue-500" />
          <h1 className="text-2xl font-bold mb-2">Authentication Required</h1>
          <p className="text-muted-foreground mb-4">
            You need to sign in to access the admin panel.
          </p>
          <Button onClick={() => router.push("/")} variant="outline">
            Go to Login
          </Button>
        </Card>
      </div>
    );
  }

  // Not admin
  if (!userProfileQuery.data || userProfileQuery.data.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access this page. Admin role required.
          </p>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Current role: {userProfileQuery.data?.role || 'none'}
            </p>
            <Button onClick={() => router.push("/")} variant="outline">
              Go Home
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInputChange = (field: keyof ContentFormData, value: string) => {
    const array = value.split(',').map(item => item.trim()).filter(Boolean);
    setFormData(prev => ({
      ...prev,
      [field]: array
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setFormErrors({});
    
    // Validate form data
    const result = contentFormSchema.safeParse(formData);
    
    if (!result.success) {
      // Extract and set validation errors
      const errors: FormErrors = {};
      result.error.issues.forEach((issue) => {
        const fieldName = issue.path[0] as keyof ContentFormData;
        errors[fieldName] = issue.message;
      });
      setFormErrors(errors);
      toast.error("Please fix the validation errors before submitting.");
      return;
    }
    
    // Submit validated data
    createContentMutation.mutate(result.data);
  };

  const handleDelete = (id: number) => {
    if (window.confirm("Are you sure you want to delete this content?")) {
      deleteContentMutation.mutate({ id });
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <AuthDebugPanel />
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {userProfileQuery.data?.full_name || userProfileQuery.data?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={() => updateStatsMutation.mutate()}
              variant="outline"
              className="flex items-center gap-2"
              disabled={updateStatsMutation.isPending}
            >
              <BarChart3 className="h-4 w-4" />
              {updateStatsMutation.isPending ? "Updating..." : "Update Stats"}
            </Button>
            <Button 
              onClick={() => setShowForm(!showForm)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Content
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <SearchBar
            onSearch={handleSearch}
            onFilter={handleFilter}
            onSort={handleSort}
            placeholder="Search admin content..."
            filters={filters}
            searchQuery={searchQuery}
          />
        </div>

        {/* Create Content Form */}
        {showForm && (
          <Card className="p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Create New Content</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Basic Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="content_uuid">Content UUID</Label>
                  <Input
                    id="content_uuid"
                    value={formData.content_uuid}
                    onChange={(e) => handleInputChange('content_uuid', e.target.value)}
                    required
                    className={formErrors.content_uuid ? "border-destructive" : ""}
                  />
                  <FieldError error={formErrors.content_uuid} />
                </div>
                
                <div>
                  <Label htmlFor="content_link">Content Link</Label>
                  <Input
                    id="content_link"
                    type="url"
                    value={formData.content_link}
                    onChange={(e) => handleInputChange('content_link', e.target.value)}
                    required
                    className={formErrors.content_link ? "border-destructive" : ""}
                  />
                  <FieldError error={formErrors.content_link} />
                </div>
                
                <div>
                  <Label htmlFor="content_account">Content Account</Label>
                  <Input
                    id="content_account"
                    value={formData.content_account}
                    onChange={(e) => handleInputChange('content_account', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_created_date">Created Date</Label>
                  <Input
                    id="content_created_date"
                    type="date"
                    value={formData.content_created_date}
                    onChange={(e) => handleInputChange('content_created_date', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_title">Title (optional)</Label>
                  <Input
                    id="content_title"
                    value={formData.content_title || ''}
                    onChange={(e) => handleInputChange('content_title', e.target.value || null)}
                  />
                </div>
                
                <div>
                  <SingleSelectDropdown
                    label="Twitter Content Type"
                    value={formData.twitter_content_type}
                    options={TWITTER_CONTENT_TYPES}
                    onChange={(value) => handleInputChange('twitter_content_type', value)}
                    placeholder="Select content type..."
                    allowNull={true}
                  />
                </div>
                </div>
              </div>
              
              {/* Twitter Metrics Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Twitter Metrics</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="twitter_impressions">Impressions</Label>
                    <Input
                      id="twitter_impressions"
                      type="number"
                      value={formData.twitter_impressions}
                      onChange={(e) => handleInputChange('twitter_impressions', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitter_likes">Likes</Label>
                    <Input
                      id="twitter_likes"
                      type="number"
                      value={formData.twitter_likes}
                      onChange={(e) => handleInputChange('twitter_likes', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitter_retweets">Retweets</Label>
                    <Input
                      id="twitter_retweets"
                      type="number"
                      value={formData.twitter_retweets}
                      onChange={(e) => handleInputChange('twitter_retweets', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                </div>
              </div>

              {/* Content Details Section */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Details</h3>
                <div>
                  <Label htmlFor="content_description">Description (optional)</Label>
                  <Input
                    id="content_description"
                    value={formData.content_description || ''}
                    onChange={(e) => handleInputChange('content_description', e.target.value || null)}
                    placeholder="Brief description of the content..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="content_tags">Tags (comma-separated)</Label>
                  <Input
                    id="content_tags"
                    value={formData.content_tags.join(', ')}
                    onChange={(e) => handleArrayInputChange('content_tags', e.target.value)}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <MultiSelectDropdown
                      label="Content Types"
                      value={formData.content_types}
                      options={CONTENT_TYPES}
                      onChange={(value) => handleInputChange('content_types', value)}
                      placeholder="Select content types..."
                    />
                  </div>
                  
                  <div>
                    <MultiSelectDropdown
                      label="Categories"
                      value={formData.content_categories}
                      options={CONTENT_CATEGORIES}
                      onChange={(value) => handleInputChange('content_categories', value)}
                      placeholder="Select categories..."
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button 
                  type="submit" 
                  disabled={createContentMutation.isPending}
                >
                  {createContentMutation.isPending ? "Creating..." : "Create Content"}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Card>
        )}

        {/* Content List */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">
            {isSearchActive ? `Search Results${searchQuery ? ` for "${searchQuery}"` : ''}` : 'Manage Content'}
          </h2>

          {(isSearchActive ? filteredContentQuery.isLoading : contentQuery.isLoading) ? (
            <div className="flex justify-center py-8">
              <Loader />
            </div>
          ) : (
            <div className="space-y-4">
              {(isSearchActive ? filteredContentQuery.data : contentQuery.data)?.map((content) => (
                <div
                  key={content.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="space-y-2">
                      <h3 className="font-medium text-lg">
                        {content.content_title || content.content_account}
                      </h3>

                      {content.content_description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {content.content_description}
                        </p>
                      )}

                      <div className="flex items-center gap-2">
                        <a
                          href={content.content_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-1 transition-colors"
                        >
                          Link
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </div>

                      {/* Content Types and Categories */}
                      <div className="flex flex-wrap gap-1 mt-2">
                        {content.content_types?.map((type) => (
                          <Badge key={type} variant="secondary" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                        {content.twitter_content_type && (
                          <Badge variant="outline" className="text-xs">
                            {content.twitter_content_type}
                          </Badge>
                        )}
                        {content.content_categories?.slice(0, 3).map((category) => (
                          <Badge key={category} variant="outline" className="text-xs">
                            #{category}
                          </Badge>
                        ))}
                        {content.content_categories && content.content_categories.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{content.content_categories.length - 3} more
                          </Badge>
                        )}
                      </div>

                      <div className="flex gap-4 text-xs text-muted-foreground mt-2">
                        <span>👁 {content.twitter_impressions?.toLocaleString() || 0}</span>
                        <span>❤️ {content.twitter_likes?.toLocaleString() || 0}</span>
                        <span>🔄 {content.twitter_retweets?.toLocaleString() || 0}</span>
                        <span>📅 {formatDate(content.content_created_date)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        // TODO: Implement edit functionality
                        toast.info("Edit functionality coming soon!");
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(content.id)}
                      disabled={deleteContentMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {(isSearchActive ? filteredContentQuery.data : contentQuery.data)?.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  {isSearchActive
                    ? "No content found matching your search criteria."
                    : "No content found. Create your first entry!"
                  }
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}

export default function AdminPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><Loader /></div>}>
      <AdminPageContent />
    </Suspense>
  );
}