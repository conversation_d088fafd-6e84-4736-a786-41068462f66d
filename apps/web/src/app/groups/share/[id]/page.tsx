"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Loader from "@/components/loader";
import { ArrowLeft, Share2, Copy, ExternalLink, Calendar, Users, Globe } from "lucide-react";
import { EngagementMetrics } from "@/components/ui/engagement-metrics";
import { PlatformIcon } from "@/components/ui/platform-icon";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface ShareGroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ShareGroupPage({ params }: ShareGroupPageProps) {
  const resolvedParams = use(params);
  const router = useRouter();

  const { data: group, isLoading } = trpc.getPublicGroup.useQuery({ id: resolvedParams.id });

  const copyToClipboard = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success("Link copied to clipboard!");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
            <Globe className="h-8 w-8 text-gray-400" />
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Group not found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            This group may be private or no longer exists.
          </p>
          <Button onClick={() => router.push("/groups")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Browse Public Groups
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      {/* Header with sharing info */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            onClick={() => router.push("/groups")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Browse Groups</span>
            <span className="sm:hidden">Back</span>
          </Button>
          
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Globe className="h-4 w-4" />
            <span>Public Group</span>
          </div>
        </div>

        {/* No login required indicator */}
        <div className="flex items-center gap-2 px-3 py-1.5 bg-green-50 dark:bg-green-950/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium border border-green-200 dark:border-green-800">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>No sign-up required</span>
        </div>
      </div>

      {/* Group info with sharing banner */}
      <div className="mb-6 sm:mb-8">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-green-100 dark:border-green-900">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-lg sm:text-xl font-bold">{group.name.charAt(0).toUpperCase()}</span>
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                      {group.name}
                    </h1>
                    <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <Globe className="h-3 w-3 mr-1" />
                      Public
                    </Badge>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{group.content_count} {group.content_count === 1 ? 'item' : 'items'}</span>
                      </div>
                      <span>Created {new Date(group.created_at).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                    <div className="text-green-600 dark:text-green-400 font-medium">
                      • Freely shared by the community
                    </div>
                  </div>
                </div>
              </div>
              
              {group.description && (
                <div className="bg-white/60 dark:bg-gray-800/60 rounded-lg p-4 backdrop-blur-sm">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {group.description}
                  </p>
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={copyToClipboard}
                className="flex items-center gap-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700"
              >
                <Copy className="h-4 w-4" />
                <span>Copy Link</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: group.name,
                      text: group.description || `Check out this group: ${group.name}`,
                      url: window.location.href,
                    });
                  } else {
                    copyToClipboard();
                  }
                }}
                className="flex items-center gap-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700"
              >
                <Share2 className="h-4 w-4" />
                <span>Share</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Content Collection
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {group.content?.length || 0} {(group.content?.length || 0) === 1 ? 'item' : 'items'} shared publicly
            </p>
          </div>
        </div>
      </div>

      {/* Content grid */}
      {group.content && group.content.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
          {group.content.map((content) => {
            // Determine platform from content or default to twitter
            const platform = content.content_link?.includes('twitter.com') || content.content_link?.includes('x.com') ? 'twitter' :
                            content.content_link?.includes('linkedin.com') ? 'linkedin' :
                            content.content_link?.includes('youtube.com') ? 'youtube' : 'other';
            
            return (
              <Card key={content.id} className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-l-4 border-l-green-500">
                {/* Header Section */}
                <CardHeader className="pb-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <PlatformIcon platform={platform} size="md" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold line-clamp-2 text-gray-900 dark:text-white mb-2">
                        {content.content_title || `Content from @${content.content_account}`}
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span className="font-medium">@{content.content_account}</span>
                        <span className="text-gray-300">•</span>
                        <span>{new Date(content.content_created_date).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Content Preview */}
                  {content.content_description && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                        {content.content_description}
                      </p>
                    </div>
                  )}

                  {/* Engagement Metrics */}
                  {content.twitter_impressions > 0 && (
                    <EngagementMetrics
                      impressions={content.twitter_impressions}
                      likes={content.twitter_likes}
                      retweets={content.twitter_retweets}
                      variant="compact"
                    />
                  )}

                  {/* Tags */}
                  {content.content_tags && content.content_tags.length > 0 && (() => {
                    const filteredTags = content.content_tags.filter(tag => !tag.includes('follow_increase'));
                    return filteredTags.length > 0 && (
                      <div className="flex flex-wrap gap-1.5">
                        {filteredTags.slice(0, 4).map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs px-2 py-1 bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/50 dark:text-green-300"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {filteredTags.length > 4 && (
                          <Badge variant="outline" className="text-xs px-2 py-1">
                            +{filteredTags.length - 4}
                          </Badge>
                        )}
                      </div>
                    );
                  })()}

                  {/* Action Section */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-800">
                    <div className="text-xs text-gray-500">
                      Added {new Date(content.added_at).toLocaleDateString()}
                    </div>
                    
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => window.open(content.content_link, "_blank")}
                      className="bg-green-600 hover:bg-green-700 text-white transition-colors"
                    >
                      <ExternalLink className="h-3 w-3 mr-2" />
                      <span className="text-sm font-medium">View Content</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card className="text-center py-16 border-dashed border-2 border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
          <CardContent>
            <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-6">
              <div className="text-2xl">📋</div>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              No content in this group yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              This public group is empty, but you can still share it with others!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}