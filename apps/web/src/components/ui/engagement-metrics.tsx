import * as React from "react"
import { <PERSON>, MessageCircle, Repeat2, Eye, TrendingUp } from "lucide-react"
import { cn } from "@/lib/utils"

interface EngagementMetricsProps {
  impressions?: number
  likes?: number
  retweets?: number
  replies?: number
  className?: string
  variant?: "default" | "compact" | "detailed"
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

export function EngagementMetrics({
  impressions = 0,
  likes = 0,
  retweets = 0,
  replies = 0,
  className,
  variant = "default"
}: EngagementMetricsProps) {
  const metrics = [
    { value: impressions, icon: Eye, label: "views", color: "text-blue-500" },
    { value: likes, icon: Heart, label: "likes", color: "text-red-500" },
    { value: retweets, icon: Repeat2, label: "retweets", color: "text-green-500" },
    { value: replies, icon: MessageCircle, label: "replies", color: "text-purple-500" }
  ].filter(metric => metric.value > 0)

  if (metrics.length === 0) return null

  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-2 sm:gap-3 text-sm text-gray-600 dark:text-gray-400 flex-wrap", className)}>
        {impressions > 0 && (
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-blue-500" />
            <span className="text-xs font-medium">{formatNumber(impressions)}</span>
          </div>
        )}
        {likes > 0 && (
          <span className="text-xs">♥ {formatNumber(likes)}</span>
        )}
        {retweets > 0 && (
          <span className="text-xs">🔄 {formatNumber(retweets)}</span>
        )}
      </div>
    )
  }

  if (variant === "detailed") {
    return (
      <div className={cn("grid grid-cols-2 sm:grid-cols-4 gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg", className)}>
        {metrics.map(({ value, icon: Icon, label, color }) => (
          <div key={label} className="flex flex-col items-center text-center">
            <div className={cn("flex items-center gap-1 mb-1", color)}>
              <Icon className="h-3 w-3" />
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                {label}
              </span>
            </div>
            <span className="text-sm font-semibold text-gray-900 dark:text-white">
              {formatNumber(value)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn("flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400", className)}>
      {metrics.map(({ value, icon: Icon, label, color }) => (
        <div key={label} className="flex items-center gap-1">
          <Icon className={cn("h-3 w-3", color)} />
          <span className="text-xs font-medium">{formatNumber(value)}</span>
        </div>
      ))}
    </div>
  )
}